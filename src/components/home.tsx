import { useRef, useState } from "react";
import { EditorState } from "../types";
import { Preview } from "./preview";
import { Toolbar } from "./toolbar";
import { Features } from "./features";
import { HowTo } from "./howto";
import { FAQ } from "./faq";
import { About } from "./about";
import { Testimonials } from "./testimonials";
import { CTA } from "./cta";
import { useRandomSound } from "../utils";

export function Home() {
  useRandomSound(0.001);

  const [state, setState] = useState<EditorState>({
    text: "Invincible",
    color: "#ebed00",
    showCredits: true,
    showWatermark: true,
    background: "url('/backgrounds/blue.jpg') no-repeat center center / cover",
    fontSize: 24,
    outline: 0,
    subtitleOffset: 0,
    outlineColor: "black",
    effect: null,
    generating: false,
    smallSubtitle: "BASED ON THE COMIC BOOK BY",
    subtitle: "<PERSON>, <PERSON>, & <PERSON>",
  });

  const canvasRef = useRef<HTMLDivElement>(null);

  return (
    <>
      {/* Hero Section */}
      <section className="bg-gradient-to-b from-slate-900 to-slate-950 py-12 md:py-20">
        <div className="container mx-auto px-4 text-center">
          <h1 className="text-4xl md:text-6xl font-bold mb-6 bg-gradient-to-r from-yellow-400 to-yellow-600 bg-clip-text text-transparent">
            Invincible Title Card Maker
          </h1>
          <p className="text-xl md:text-2xl text-slate-300 mb-8 max-w-5xl mx-auto">
            Design professional Invincible title card graphics with our free online editor. Customize text, colors, and backgrounds to create stunning title cards inspired by the hit animated series.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <a href="#editor" className="bg-yellow-500 hover:bg-yellow-600 text-black font-bold py-3 px-8 rounded-lg transition-colors">
              Start Creating
            </a>
            <a href="#faq" className="border border-yellow-500 text-yellow-500 hover:bg-yellow-500 hover:text-black font-bold py-3 px-8 rounded-lg transition-colors">
              View FAQ
            </a>
          </div>
        </div>
      </section>

      {/* Editor Section */}
      <main id="main-content" className="flex-1">
        <section id="editor" className="py-12 md:py-20 bg-slate-950">
          <div className="container mx-auto px-4">
            {/* Section Header */}
            <div className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl font-bold mb-4 bg-gradient-to-r from-yellow-400 to-yellow-600 bg-clip-text text-transparent">
                Create Your Title Card
              </h2>
              <p className="text-lg text-slate-400 max-w-2xl mx-auto">
                Customize every aspect of your Invincible title card with our intuitive editor
              </p>
            </div>

            {/* Editor Layout */}
            <div className="grid lg:grid-cols-3 gap-8 items-start">
              {/* Preview Section - Takes up 2/3 on large screens */}
              <div className="lg:col-span-2 order-2 lg:order-1">
                <div className="bg-slate-900 rounded-2xl p-6 border border-slate-700 shadow-2xl">
                  <div className="mb-4">
                    <h3 className="text-xl font-semibold text-slate-200 mb-2">Preview</h3>
                    <p className="text-sm text-slate-400">See your title card in real-time</p>
                  </div>
                  <Preview canvasRef={canvasRef} state={state} />
                  {/* <AdBanner
                    data-ad-format="fluid"
                    data-ad-slot="6767948661"
                    data-full-width-responsive="true"
                    style={{
                      width: "100%",
                      minHeight: 100,
                      maxHeight: 100,
                    }}
                  /> */}
                </div>
              </div>

              {/* Toolbar Section - Takes up 1/3 on large screens */}
              <div className="lg:col-span-1 order-1 lg:order-2">
                <div className="bg-slate-900 rounded-2xl border border-slate-700 shadow-2xl h-fit sticky top-8">
                  <div className="p-6 border-b border-slate-700">
                    <h3 className="text-xl font-semibold text-slate-200 mb-2">Customize</h3>
                    <p className="text-sm text-slate-400">Adjust settings and styling</p>
                  </div>
                  <div className="max-h-[calc(100vh-300px)] overflow-y-auto">
                    <Toolbar canvasRef={canvasRef} state={state} setState={setState} />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>
      </main>

      {/* Features Section */}
      <Features />

      {/* How To Use Section */}
      <HowTo />

      {/* FAQ Section */}
      <FAQ />

      {/* About Section */}
      <About />

      {/* Testimonials Section */}
      <Testimonials />

      {/* CTA Section */}
      <CTA />
    </>
  );
}
